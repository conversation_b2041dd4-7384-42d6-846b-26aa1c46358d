package org.example;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;

/**
 * <p>文件名称:org.example.Gif2binExample</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/7/8
 */
public class Gif2binExample {
    /**
     * 示例1：获取字节流数据并写入文件
     */
    public static void example1_BasicUsage(String gifPath, String outputPath) throws IOException {
        System.out.println("=== 示例1：基本用法 ===");

        // 获取二进制数据（未压缩）
        byte[] binData = Image2binUtil.convertGif2BinData(new File(gifPath), Image2binUtil.ColorMode.RGB565);

        // 外层处理数据（这里可以进行任何自定义处理）
        System.out.println("获取到二进制数据，长度: " + binData.length + " 字节");

        // 写入文件
        writeBinDataToFile(binData, outputPath);
    }

    private static void writeBinDataToFile(byte[] binData, String outputPath) throws IOException {
        File file = new File(outputPath);
        //try (OutputStream os = Files.newOutputStream(file.toPath(), StandardOpenOption.CREATE, java.nio.file.StandardOpenOption.DELETE_ON_CLOSE);) {
        //    os.write(binData);
        //}

        try (FileOutputStream fos = new FileOutputStream(outputPath);) {
            fos.write(binData);
        }
    }

    /**
     * 示例2：指定尺寸转换
     */
    public static void example2_WithSize(String gifPath, String outputPath, int width, int height) throws IOException {
        System.out.println("=== 示例2：指定尺寸转换 ===");

        // 获取指定尺寸的二进制数据（未压缩）
        byte[] binData = Image2binUtil.convertGif2BinData(new File(gifPath), width, height, null, Image2binUtil.ColorMode.RGB565A8, true);

        // 可以在这里对数据进行处理
        System.out.println("转换为 " + width + "x" + height + " 尺寸");
        System.out.println("数据大小: " + binData.length + " 字节");

        // 写入文件
        writeBinDataToFile(binData, outputPath);
    }

    /**
     * 示例3：限制帧数并进行自定义处理
     */
    public static void example3_WithFrameLimit(String gifPath, String outputPath, int width, int height, int maxFrames) throws IOException {
        System.out.println("=== 示例3：限制帧数并自定义处理 ===");

        // 获取限制帧数的二进制数据（启用压缩）
        byte[] binData = Image2binUtil.convertGif2BinData(new File(gifPath), width, height, maxFrames, Image2binUtil.ColorMode.RGB565A8, true);

        // 自定义处理：分析数据头部
        analyzeHeader(binData);

        // 可以进行其他处理，比如压缩、加密等
        //byte[] processedData = customProcessing(binData);

        // 写入处理后的数据
        writeBinDataToFile(binData, outputPath);
    }

    /**
     * 示例4：批量处理多个文件
     */
    public static void example4_BatchProcessing(String[] gifPaths, String outputDir) throws IOException {
        System.out.println("=== 示例4：批量处理 ===");

        for (int i = 0; i < gifPaths.length; i++) {
            String gifPath = gifPaths[i];
            String outputPath = outputDir + "/output_" + i + ".bin";

            try {
                // 获取数据（启用压缩）
                byte[] binData = Image2binUtil.convertGif2BinData(new File(gifPath), 64, 64, 20, Image2binUtil.ColorMode.RGB565, true);

                // 批量处理逻辑
                System.out.println("处理文件 " + (i + 1) + "/" + gifPaths.length + ": " + gifPath);

                // 写入文件
                writeBinDataToFile(binData, outputPath);

            } catch (IOException e) {
                System.err.println("处理文件失败: " + gifPath + " - " + e.getMessage());
            }
        }
    }

    /**
     * 示例5：压缩效果对比
     */
    public static void example5_CompressionComparison(String gifPath, String outputDir) throws IOException {
        System.out.println("=== 示例5：压缩效果对比 ===");

        // 未压缩版本
        byte[] uncompressedData = Image2binUtil.convertGif2BinData(new File(gifPath), 64, 64, 20, Image2binUtil.ColorMode.RGB565, false);
        String uncompressedPath = outputDir + "/uncompressed.bin";
        writeBinDataToFile(uncompressedData, uncompressedPath);

        // 压缩版本
        byte[] compressedData = Image2binUtil.convertGif2BinData(new File(gifPath), 64, 64, 20, Image2binUtil.ColorMode.RGB565, true);
        String compressedPath = outputDir + "/compressed.bin";
        writeBinDataToFile(compressedData, compressedPath);

        // 对比结果
        double compressionRatio = (double) compressedData.length / uncompressedData.length;
        double spaceSaved = (1.0 - compressionRatio) * 100;

        System.out.println("压缩效果对比:");
        System.out.println("  未压缩大小: " + uncompressedData.length + " 字节 (" + String.format("%.2f", uncompressedData.length / 1024.0) + " KB)");
        System.out.println("  压缩后大小: " + compressedData.length + " 字节 (" + String.format("%.2f", compressedData.length / 1024.0) + " KB)");
        System.out.println("  压缩比: " + String.format("%.2f", compressionRatio * 100) + "%");
        System.out.println("  节省空间: " + String.format("%.2f", spaceSaved) + "%");
    }

    /**
     * 示例6：处理PNG序列
     */
    public static void example6_PngSequence(String pngSequenceFolderPath, String outputPath) throws IOException {
        System.out.println("=== 示例6：处理PNG序列 ===");

        File pngSequenceFolder = new File(pngSequenceFolderPath);
        if (!pngSequenceFolder.exists() || !pngSequenceFolder.isDirectory()) {
            System.err.println("PNG序列文件夹不存在或不是目录: " + pngSequenceFolderPath);
            return;
        }

        // 获取PNG序列的二进制数据（启用压缩）
        byte[] binData = ImagePngSe2binUtil.convertPngSequence2BinData(
            pngSequenceFolder, 180, 320, null, ImagePngSe2binUtil.ColorMode.RGB565A8, true);

        if (binData == null) {
            System.err.println("处理PNG序列失败");
            return;
        }

        System.out.println("成功处理PNG序列，获取到二进制数据，长度: " + binData.length + " 字节");

        // 写入文件
        writeBinDataToFile(binData, outputPath);
        System.out.println("已将二进制数据写入文件: " + outputPath);

        // 分析头部信息
        analyzeHeader(binData);
    }

    /**
     * 分析二进制数据头部信息
     */
    private static void analyzeHeader(byte[] binData) {
        if (binData.length < 13) {
            System.out.println("数据太短，无法分析头部");
            return;
        }

        // 解析头部信息（小端序）
        int frameCount = (binData[3] & 0xFF) << 24 | (binData[2] & 0xFF) << 16 |
                (binData[1] & 0xFF) << 8 | (binData[0] & 0xFF);
        int width = (binData[7] & 0xFF) << 24 | (binData[6] & 0xFF) << 16 |
                (binData[5] & 0xFF) << 8 | (binData[4] & 0xFF);
        int height = (binData[11] & 0xFF) << 24 | (binData[10] & 0xFF) << 16 |
                (binData[9] & 0xFF) << 8 | (binData[8] & 0xFF);
        int colorMode = binData[12] & 0xFF;

        System.out.println("头部信息分析:");
        System.out.println("  帧数: " + frameCount);
        System.out.println("  宽度: " + width);
        System.out.println("  高度: " + height);
        System.out.println("  色彩模式: " + colorMode + " (" + getColorModeName(colorMode) + ")");
        System.out.println("  预期数据大小: " + (width * height * frameCount * getColorModeBytes(colorMode)) + " 字节");
    }

    /**
     * 自定义数据处理（示例：简单的数据验证）
     */
    private static byte[] customProcessing(byte[] binData) {
        System.out.println("执行自定义处理...");

        // 这里可以进行各种自定义处理
        // 例如：数据压缩、加密、格式转换等

        // 示例：添加简单的校验和
        byte[] processedData = new byte[binData.length + 4];
        System.arraycopy(binData, 0, processedData, 0, binData.length);

        // 计算简单校验和
        int checksum = 0;
        for (byte b : binData) {
            checksum += b & 0xFF;
        }

        // 添加校验和到末尾（小端序）
        processedData[binData.length] = (byte) (checksum & 0xFF);
        processedData[binData.length + 1] = (byte) ((checksum >> 8) & 0xFF);
        processedData[binData.length + 2] = (byte) ((checksum >> 16) & 0xFF);
        processedData[binData.length + 3] = (byte) ((checksum >> 24) & 0xFF);

        System.out.println("添加校验和: " + checksum);

        return processedData;
    }

    /**
     * 获取色彩模式名称
     */
    private static String getColorModeName(int colorMode) {
        switch (colorMode) {
            case 1: return "RGB565";
            case 2: return "RGB565A8";
            case 3: return "ARGB8888";
            default: return "未知";
        }
    }

    /**
     * 获取色彩模式字节数
     */
    private static int getColorModeBytes(int colorMode) {
        switch (colorMode) {
            case 1: return 2; // RGB565
            case 2: return 3; // RGB565A8
            case 3: return 4; // ARGB8888
            default: return 2;
        }
    }


    public static void example7_WithFrameLimitMP4(String MP4Path, String outputPath, int width, int height, int maxFrames) throws IOException {
        System.out.println("=== 示例3：限制帧数并自定义处理 ===");

        // 获取限制帧数的二进制数据（启用压缩）
        byte[] binData = Image2binUtil.convertGif2BinData(new File(MP4Path), width, height, maxFrames, Image2binUtil.ColorMode.RGB565, true);

        // 自定义处理：分析数据头部
        analyzeHeader(binData);

        // 可以进行其他处理，比如压缩、加密等
        //byte[] processedData = customProcessing(binData);

        // 写入处理后的数据
        writeBinDataToFile(binData, outputPath);
    }

    /**
     * 主方法：演示各种用法
     */
    public static void main(String[] args) {
        //if (args.length < 1) {
        //    System.out.println("用法: java Gif2binExample <gif文件路径>");
        //    return;
        //}

        //String gifPath = "/Users/<USER>/Downloads/142230-8帧/shy.gif";
        //String gifPath = "/Users/<USER>/Downloads/飞书20250710-190722.gif";
        //String gifPath = "/Users/<USER>/Downloads/20250716-162631.gif";

        try {
            // 示例1：基本用法
            //example1_BasicUsage(gifPath, "output1.bin");
            //
            //// 示例2：指定尺寸
            //example2_WithSize(gifPath, "output2.bin", 64, 64);
            //
            //// 示例3：限制帧数
            //example3_WithFrameLimit(gifPath, "output3.bin", 240, 320, 40);
            //
            //// 示例5：压缩效果对比
            //example5_CompressionComparison(gifPath, ".");

            // 示例6：处理PNG序列（如果有PNG序列文件夹）
            // 注意：请将路径替换为实际的PNG序列文件夹路径
            String pngSequenceFolderPath = "/Users/<USER>/Downloads/test_mp4";
            //String pngSequenceFolderPath = "/Users/<USER>/data/store/a";
            //String mp4Path = "/Users/<USER>/Downloads/20250807-153911.mp4";
            example6_PngSequence(pngSequenceFolderPath, "output_png_sequence_081901.bin");
            //if (new File(mp4Path).exists()) {
                //example7_WithFrameLimitMP4(mp4Path, "output_mp4_071801.bin", 360, 360, 50);
            //} else {
            //    System.out.println("\n跳过示例6：PNG序列文件夹不存在 - " + mp4Path);
            //}

            System.out.println("\n所有示例执行完成！");

        } catch (IOException e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
