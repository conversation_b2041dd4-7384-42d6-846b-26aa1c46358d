package org.example;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;


public class ImagePngSe2binUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(ImagePngSe2binUtil.class);

    /**
     * 色彩解码模式
     */
    public enum ColorMode {
        /*CG大图*/RGB565((img) -> {
            //2Bytes，RGB565，不带alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            ByteBuffer buffer = ByteBuffer.allocate(width * height * 2);
            buffer.order(ByteOrder.LITTLE_ENDIAN);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int rgb = img.getRGB(x, y);
                    int r = (rgb >> 16) & 0xFF;
                    int g = (rgb >> 8) & 0xFF;
                    int b = rgb & 0xFF;

                    // 转换为RGB565格式
                    int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
                    buffer.putShort((short) pixel);
                }
            }
            return buffer.array();
        }),
        /*人物图（背景透明）*/RGB565A8((img) -> {
            //3 Bytes，RGB565 + A8 +alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            ByteBuffer buffer = ByteBuffer.allocate(width * height * 3);
            buffer.order(ByteOrder.LITTLE_ENDIAN); //小端序
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int argb = img.getRGB(x, y);
                    int a = (argb >> 24) & 0xFF;
                    int r = (argb >> 16) & 0xFF;
                    int g = (argb >> 8) & 0xFF;
                    int b = argb & 0xFF;

                    int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);  //RGB565
                    buffer.putShort((short) pixel);
                    buffer.put((byte) a); //Alpha
                }
            }
            return buffer.array();
        }),
        ARGB8888((img) -> {
            //4Bytes，RGBA，带alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            byte[] data = new byte[width * height * 4];
            int index = 0;
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int argb = img.getRGB(x, y);
                    int a = (argb >> 24) & 0xFF;
                    int r = (argb >> 16) & 0xFF;
                    int g = (argb >> 8) & 0xFF;
                    int b = argb & 0xFF;

                    // BGRA顺序（小端序）
                    data[index++] = (byte) b;
                    data[index++] = (byte) g;
                    data[index++] = (byte) r;
                    data[index++] = (byte) a;
                }
            }
            return data;
        });

        private final Function<BufferedImage, byte[]> converter;

        ColorMode(Function<BufferedImage, byte[]> converter) {
            this.converter = converter;
        }

        public byte[] convert(BufferedImage image) {
            return converter.apply(image);
        }
    }

    /**
     * 转换GIF文件为二进制数据
     * - 需要写入文件请使用：
     * try (OutputStream os = Files.newOutputStream(new File(outputPath).toPath(), StandardOpenOption.CREATE, StandardOpenOption.DELETE_ON_CLOSE);) {
     * os.write(binData);
     * }
     *
     * @param gifFile
     * @return {@link byte[] }
     * @throws IOException ioexception
     */
    public static byte[] convertGif2BinData(File gifFile, ColorMode colorMode) {
        try {
            return convertGif2BinData(gifFile, null, null, null, colorMode, true);
        } catch (IOException e) {
            LOGGER.error("Image2binUtil.convertGif2BinData {}.转换GIF文件失败: {}", gifFile.getName(), e.getMessage());
            return null;    // passed exception without handling;
        }
    }

    /**
     * 转换PNG序列文件夹为二进制数据
     * - 需要写入文件请使用：
     * try (OutputStream os = Files.newOutputStream(new File(outputPath).toPath(), StandardOpenOption.CREATE, StandardOpenOption.DELETE_ON_CLOSE);) {
     * os.write(binData);
     * }
     *
     * @param pngSequenceFolder PNG序列文件夹，包含frame_000000.png等文件
     * @param colorMode 色彩模式
     * @return {@link byte[] }
     */
    public static byte[] convertPngSequence2BinData(File pngSequenceFolder, ColorMode colorMode) {
        try {
            return convertPngSequence2BinData(pngSequenceFolder, null, null, null, colorMode, true);
        } catch (IOException e) {
            LOGGER.error("ImagePngSe2binUtil.convertPngSequence2BinData {}.转换PNG序列文件夹失败: {}", pngSequenceFolder.getName(), e.getMessage());
            return null;
        }
    }

    public static byte[] convertPngSequence2BinData(File pngSequenceFolder, Integer width, Integer height, Integer maxFrames, ColorMode colorMode, boolean enableCompression) throws IOException {
        List<BufferedImage> frames = ImagePngSe2binUtil.readPngSequenceFrames(pngSequenceFolder);
        return processFramesToBinData(frames, width, height, maxFrames, colorMode, enableCompression, pngSequenceFolder.getName());
    }

    public static byte[] convertGif2BinData(File gifFile, Integer width, Integer height, Integer maxFrames, ColorMode colorMode, boolean enableCompression) throws IOException {
        List<BufferedImage> frames = Image2binUtil.readFrames(gifFile);
        return processFramesToBinData(frames, width, height, maxFrames, colorMode, enableCompression, gifFile.getName());
    }

    private static byte[] processFramesToBinData(List<BufferedImage> frames, Integer width, Integer height, Integer maxFrames, ColorMode colorMode, boolean enableCompression, String sourceName) throws IOException {
        //重绘画面
        if (Objects.isNull(width) || Objects.isNull(height)) {
            BufferedImage firstFrame = frames.get(0);
            width = firstFrame.getWidth();
            height = firstFrame.getHeight();
        } else {
            for (int i = 0; i < frames.size(); ++i) {
                BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = resized.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.drawImage(frames.get(i), 0, 0, width, height, null);
                g2d.dispose();
                frames.set(i, resized);
            }
        }
        // 帧降采样 - 基于FPS的精确采样
        if (!Objects.isNull(maxFrames) && frames.size() > maxFrames) {
            frames = sampleFramesByFps(frames, maxFrames);
        }

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             DataOutputStream dos = new DataOutputStream(baos)) {
            /*Tip：设备端（宇敏）定制的头信息，皆使用小端序：
                总帧数(4),
                分辨率-宽(4),
                分辨率-高(4),
                色彩模式-(1), //1-RGB565, 2-RGB565A8
                每数据帧的起始位置和大小(8),
                数据帧
            */
            dos.writeInt(Integer.reverseBytes(frames.size()));
            dos.writeInt(Integer.reverseBytes(width));
            dos.writeInt(Integer.reverseBytes(height));
            dos.writeByte(colorMode.ordinal() + 1);

            int dataOffset = 13 + frames.size() * 8;
            // 准备帧数据
            List<byte[]> frameDataList = new ArrayList<>();
            long totalOriginalSize = 0;
            long totalCompressedSize = 0;

            for (BufferedImage frame : frames) {
                byte[] originalFrameData = colorMode.convert(frame);
                totalOriginalSize += originalFrameData.length;
                byte[] frameData;
                if (enableCompression) {
                    frameData = ImagePngSe2binUtil.compressDataUsedZlib(originalFrameData);
                } else {
                    frameData = originalFrameData;
                }
                totalCompressedSize += frameData.length;
                frameDataList.add(frameData);

                dos.writeInt(Integer.reverseBytes(dataOffset));
                dos.writeInt(Integer.reverseBytes(frameData.length));
                dataOffset += frameData.length;
            }
            for (byte[] frameData : frameDataList) {
                dos.write(frameData);
            }
            dos.flush();
            byte[] result = baos.toByteArray();
            Function<Double, String> formatLogPrecision = v -> String.format("%.2f", v);
            if (enableCompression) {
                double compressionRatio = totalOriginalSize > 0 ? (double) totalCompressedSize / totalOriginalSize : 1.0;
                LOGGER.info("ImagePngSe2binUtil.processFramesToBinData.output {}.生成二进制数据大小: {} KB, 输出帧数:{}, 压缩比: {}% (原始: {} KB -> 压缩后: {} KB)",
                        sourceName, formatLogPrecision.apply(result.length / 1024.0), frames.size(), formatLogPrecision.apply(compressionRatio * 100), formatLogPrecision.apply(totalOriginalSize / 1024.0), formatLogPrecision.apply(totalCompressedSize / 1024.0));
            } else {
                LOGGER.info("ImagePngSe2binUtil.processFramesToBinData.output {}.生成二进制数据大小: {} KB, 输出帧数:{} (未压缩)",
                        sourceName, formatLogPrecision.apply(result.length / 1024.0), frames.size());
            }
            return result;
        }
    }

    public static List<BufferedImage> readGifFrames(File gifFile) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();
        ImageReader reader = null;
        try (ImageInputStream input = ImageIO.createImageInputStream(gifFile)) {
            Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName("gif");
            if (!readers.hasNext()) {
                throw new IOException("No GIF readers found");
            }
            reader = readers.next();
            reader.setInput(input);
            int numFrames = reader.getNumImages(true);
            ;
            for (int i = 0; i < numFrames; ++i) {
                try {
                    BufferedImage frame = reader.read(i);
                    if (Objects.isNull(frame)) {
                        continue;
                    }
                    BufferedImage convertedFrame = new BufferedImage(frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = convertedFrame.createGraphics();
                    g2d.drawImage(frame, 0, 0, null);
                    g2d.dispose();
                    frames.add(convertedFrame);
                } catch (IndexOutOfBoundsException e) {
                    break;
                } catch (Exception e) {
                    LOGGER.error("Image2binUtil.readGifFrames {}.跳过损坏的第[{}]帧: {}", gifFile.getName(), i, e.getMessage());
                    continue;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Image2binUtil.readGifFrames {}.读取GIF文件失败: {}", gifFile.getName(), e.getMessage());
        } finally {
            if (null != reader) {
                reader.dispose();
            }
        }
        if (frames.isEmpty()) {
            LOGGER.error("Image2binUtil.readGifFrames {}.无法从GIF文件中读取任何有效帧", gifFile.getName());
        }
        return frames;
    }

    /**
     * 读取PNG序列帧
     * @param pngSequenceFolder PNG序列文件夹，包含frame_000000.png等文件
     * @return 帧列表
     * @throws IOException IO异常
     */
    public static List<BufferedImage> readPngSequenceFrames(File pngSequenceFolder) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();

        if (!pngSequenceFolder.exists() || !pngSequenceFolder.isDirectory()) {
            throw new IOException("PNG序列文件夹不存在或不是目录: " + pngSequenceFolder.getPath());
        }

        // 获取所有PNG文件并按文件名排序
        File[] pngFiles = pngSequenceFolder.listFiles((dir, name) ->
            name.toLowerCase().endsWith(".png") && name.matches("\\d+\\.png"));

        if (pngFiles == null || pngFiles.length == 0) {
            throw new IOException("在文件夹中未找到符合frame_xxxxxx.png格式的PNG文件: " + pngSequenceFolder.getPath());
        }

        // 按文件名排序
        Arrays.sort(pngFiles, Comparator.comparing(File::getName));

        LOGGER.info("ImagePngSe2binUtil.readPngSequenceFrames 找到 {} 个PNG文件", pngFiles.length);

        for (File pngFile : pngFiles) {
            try {
                BufferedImage image = ImageIO.read(pngFile);
                if (image == null) {
                    LOGGER.warn("ImagePngSe2binUtil.readPngSequenceFrames 跳过无法读取的文件: {}", pngFile.getName());
                    continue;
                }

                // 转换为ARGB格式以确保一致性
                BufferedImage convertedFrame = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = convertedFrame.createGraphics();
                g2d.drawImage(image, 0, 0, null);
                g2d.dispose();
                frames.add(convertedFrame);

            } catch (Exception e) {
                LOGGER.error("ImagePngSe2binUtil.readPngSequenceFrames 读取PNG文件失败: {} - {}", pngFile.getName(), e.getMessage());
                continue;
            }
        }

        if (frames.isEmpty()) {
            throw new IOException("无法从PNG序列文件夹中读取任何有效帧: " + pngSequenceFolder.getPath());
        }

        LOGGER.info("ImagePngSe2binUtil.readPngSequenceFrames 成功读取 {} 帧", frames.size());
        return frames;
    }

    /**
     * 基于FPS的精确帧采样方法
     * 使用等间距采样确保帧分布均匀，避免简单跳跃采样导致的不均匀分布
     *
     * @param originalFrames 原始帧列表
     * @param targetFrameCount 目标帧数
     * @return 采样后的帧列表
     */
    private static List<BufferedImage> sampleFramesByFps(List<BufferedImage> originalFrames, int targetFrameCount) {
        if (originalFrames.size() <= targetFrameCount) {
            return originalFrames;
        }

        List<BufferedImage> sampledFrames = new ArrayList<>();
        double interval = (double) originalFrames.size() / targetFrameCount;

        for (int i = 0; i < targetFrameCount; i++) {
            // 使用浮点数计算精确的采样位置，然后四舍五入到最近的整数索引
            int frameIndex = (int) Math.round(i * interval);
            // 确保索引不超出范围
            frameIndex = Math.min(frameIndex, originalFrames.size() - 1);
            sampledFrames.add(originalFrames.get(frameIndex));
        }

        LOGGER.info("ImagePngSe2binUtil.sampleFramesByFps 帧采样完成: {} -> {} 帧, 采样间隔: {:.2f}",
                   originalFrames.size(), sampledFrames.size(), interval);
        return sampledFrames;
    }

    /**
     * 使用zlib压缩数据
     *
     * @param data
     * @return
     * @throws IOException
     */
    private static byte[] compressDataUsedZlib(byte[] data) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             DeflaterOutputStream deflaterStream = new DeflaterOutputStream(baos, new Deflater(6))) {
            deflaterStream.write(data);
            deflaterStream.finish();
            return baos.toByteArray();
        }
    }

}
