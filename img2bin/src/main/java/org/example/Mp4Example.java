package org.example;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.util.List;

/**
 * MP4文件处理示例
 * 演示如何使用新增的MP4帧读取功能
 */
public class Mp4Example {

    /**
     * 示例1：基本MP4处理
     */
    public static void example1_BasicMp4Processing(String mp4Path, String outputPath) throws IOException {
        System.out.println("=== 示例1：基本MP4处理 ===");
        
        File mp4File = new File(mp4Path);
        if (!mp4File.exists()) {
            System.out.println("MP4文件不存在: " + mp4Path);
            return;
        }
        
        // 使用新的readFrames方法读取MP4帧
        List<BufferedImage> frames = Image2binUtil.readFrames(mp4File);
        System.out.println("成功读取MP4文件，总帧数: " + frames.size());
        
        if (!frames.isEmpty()) {
            BufferedImage firstFrame = frames.get(0);
            System.out.println("第一帧尺寸: " + firstFrame.getWidth() + "x" + firstFrame.getHeight());
        }
        
        // 转换为二进制数据
        byte[] binData = Image2binUtil.convertGif2BinData(mp4File, Image2binUtil.ColorMode.RGB565A8);
        
        if (binData != null) {
            // 写入文件
            try {
                Files.write(new File(outputPath).toPath(), binData, 
                           StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                System.out.println("MP4转换完成，输出文件: " + outputPath);
                System.out.println("输出大小: " + binData.length / 1024.0 + " KB");
            } catch (IOException e) {
                System.err.println("写入文件失败: " + e.getMessage());
            }
        }
    }

    /**
     * 示例2：MP4转换为指定尺寸
     */
    public static void example2_Mp4WithCustomSize(String mp4Path, String outputPath, 
                                                  int width, int height) throws IOException {
        System.out.println("=== 示例2：MP4转换为指定尺寸 ===");
        
        File mp4File = new File(mp4Path);
        if (!mp4File.exists()) {
            System.out.println("MP4文件不存在: " + mp4Path);
            return;
        }
        
        // 转换为指定尺寸的二进制数据
        byte[] binData = Image2binUtil.convertGif2BinData(
            mp4File, width, height, null, 
            Image2binUtil.ColorMode.RGB565A8, true);
        
        if (binData != null) {
            System.out.println("MP4转换为 " + width + "x" + height + " 尺寸完成");
            System.out.println("压缩后数据大小: " + binData.length / 1024.0 + " KB");
            
            // 写入文件
            try {
                Files.write(new File(outputPath).toPath(), binData, 
                           StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                System.out.println("输出文件: " + outputPath);
            } catch (IOException e) {
                System.err.println("写入文件失败: " + e.getMessage());
            }
        }
    }

    /**
     * 示例3：MP4帧数限制
     */
    public static void example3_Mp4WithFrameLimit(String mp4Path, String outputPath, 
                                                  int maxFrames) throws IOException {
        System.out.println("=== 示例3：MP4帧数限制 ===");
        
        File mp4File = new File(mp4Path);
        if (!mp4File.exists()) {
            System.out.println("MP4文件不存在: " + mp4Path);
            return;
        }
        
        // 首先读取原始帧数
        List<BufferedImage> originalFrames = Image2binUtil.readFrames(mp4File);
        System.out.println("原始帧数: " + originalFrames.size());
        
        // 转换时限制帧数
        byte[] binData = Image2binUtil.convertGif2BinData(
            mp4File, null, null, maxFrames, 
            Image2binUtil.ColorMode.RGB565A8, true);
        
        if (binData != null) {
            System.out.println("限制帧数为: " + maxFrames);
            System.out.println("压缩后数据大小: " + binData.length / 1024.0 + " KB");
            
            // 写入文件
            try {
                Files.write(new File(outputPath).toPath(), binData, 
                           StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                System.out.println("输出文件: " + outputPath);
            } catch (IOException e) {
                System.err.println("写入文件失败: " + e.getMessage());
            }
        }
    }

    /**
     * 示例4：比较不同视频格式的处理
     */
    public static void example4_CompareFormats(String[] videoPaths) {
        System.out.println("=== 示例4：比较不同视频格式 ===");
        
        for (String videoPath : videoPaths) {
            File videoFile = new File(videoPath);
            if (!videoFile.exists()) {
                System.out.println("跳过不存在的文件: " + videoPath);
                continue;
            }
            
            try {
                long startTime = System.currentTimeMillis();
                List<BufferedImage> frames = Image2binUtil.readFrames(videoFile);
                long endTime = System.currentTimeMillis();
                
                System.out.println("文件: " + videoFile.getName());
                System.out.println("  格式: " + getFileExtension(videoFile.getName()));
                System.out.println("  帧数: " + frames.size());
                if (!frames.isEmpty()) {
                    BufferedImage firstFrame = frames.get(0);
                    System.out.println("  尺寸: " + firstFrame.getWidth() + "x" + firstFrame.getHeight());
                }
                System.out.println("  读取耗时: " + (endTime - startTime) + "ms");
                System.out.println();
                
            } catch (IOException e) {
                System.err.println("处理文件失败: " + videoPath + " - " + e.getMessage());
            }
        }
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    /**
     * 主方法 - 运行示例
     */
    public static void main(String[] args) {
        System.out.println("MP4处理功能示例");
        System.out.println("================");
        
        // 请根据实际情况修改文件路径
        String mp4Path = "/path/to/your/video.mp4";
        
        // 检查是否提供了命令行参数
        if (args.length > 0) {
            mp4Path = args[0];
        }
        
        try {
            // 示例1：基本处理
            example1_BasicMp4Processing(mp4Path, "output_mp4_basic.bin");
            
            // 示例2：指定尺寸
            example2_Mp4WithCustomSize(mp4Path, "output_mp4_64x64.bin", 64, 64);
            
            // 示例3：限制帧数
            example3_Mp4WithFrameLimit(mp4Path, "output_mp4_limited.bin", 30);
            
            // 示例4：格式比较（如果有多个视频文件）
            String[] testVideos = {mp4Path, "/path/to/test.avi", "/path/to/test.mov"};
            example4_CompareFormats(testVideos);
            
            System.out.println("所有示例执行完成！");
            
        } catch (IOException e) {
            System.err.println("执行示例时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
