package org.example;

import junit.framework.TestCase;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 测试MP4帧读取功能
 */
public class Mp4FrameReaderTest extends TestCase {

    /**
     * 测试readFrames方法对不支持格式的处理
     */
    public void testUnsupportedFormat() {
        try {
            File unsupportedFile = new File("test.txt");
            Image2binUtil.readFrames(unsupportedFile);
            fail("应该抛出IOException");
        } catch (IOException e) {
            assertTrue("错误信息应包含不支持的格式", e.getMessage().contains("不支持的文件格式"));
        }
    }

    /**
     * 测试GIF文件处理（向后兼容性测试）
     */
    public void testGifCompatibility() throws IOException {
        // 创建一个简单的测试图像
        BufferedImage testImage = new BufferedImage(32, 32, BufferedImage.TYPE_INT_ARGB);
        
        // 由于我们无法在测试中创建真实的GIF文件，这里主要测试方法调用路径
        // 在实际使用中，用户需要提供真实的GIF或MP4文件
        
        File gifFile = new File("test.gif");
        if (gifFile.exists()) {
            try {
                List<BufferedImage> frames = Image2binUtil.readFrames(gifFile);
                assertNotNull("帧列表不应为空", frames);
                System.out.println("成功读取GIF文件，帧数: " + frames.size());
            } catch (IOException e) {
                System.out.println("GIF文件读取失败（这在测试环境中是正常的）: " + e.getMessage());
            }
        } else {
            System.out.println("跳过GIF测试：测试文件不存在");
        }
    }

    /**
     * 测试MP4文件处理
     */
    public void testMp4Processing() {
        File mp4File = new File("test.mp4");
        if (mp4File.exists()) {
            try {
                List<BufferedImage> frames = Image2binUtil.readFrames(mp4File);
                assertNotNull("帧列表不应为空", frames);
                assertTrue("应该至少有一帧", frames.size() > 0);
                
                // 验证每一帧都是有效的BufferedImage
                for (BufferedImage frame : frames) {
                    assertNotNull("帧不应为空", frame);
                    assertTrue("帧宽度应大于0", frame.getWidth() > 0);
                    assertTrue("帧高度应大于0", frame.getHeight() > 0);
                    assertEquals("帧类型应为ARGB", BufferedImage.TYPE_INT_ARGB, frame.getType());
                }
                
                System.out.println("成功读取MP4文件，帧数: " + frames.size());
                System.out.println("第一帧尺寸: " + frames.get(0).getWidth() + "x" + frames.get(0).getHeight());
                
            } catch (IOException e) {
                System.out.println("MP4文件读取失败（可能是测试环境问题）: " + e.getMessage());
            }
        } else {
            System.out.println("跳过MP4测试：测试文件不存在");
        }
    }

    /**
     * 测试支持的视频格式识别
     */
    public void testSupportedFormats() {
        String[] supportedFormats = {".mp4", ".avi", ".mov", ".mkv"};
        String[] unsupportedFormats = {".txt", ".jpg", ".png", ".doc"};
        
        for (String format : supportedFormats) {
            File file = new File("test" + format);
            try {
                // 这里只测试格式识别，不测试实际读取
                // 因为我们没有真实的视频文件
                Image2binUtil.readFrames(file);
            } catch (IOException e) {
                // 预期会因为文件不存在而失败，但不应该是格式不支持的错误
                assertFalse("不应该是格式不支持错误: " + e.getMessage(), 
                           e.getMessage().contains("不支持的文件格式"));
            }
        }
        
        for (String format : unsupportedFormats) {
            File file = new File("test" + format);
            try {
                Image2binUtil.readFrames(file);
                fail("应该抛出不支持格式的异常: " + format);
            } catch (IOException e) {
                assertTrue("应该是格式不支持错误: " + e.getMessage(), 
                          e.getMessage().contains("不支持的文件格式"));
            }
        }
    }

    /**
     * 测试方法向后兼容性
     */
    public void testBackwardCompatibility() {
        // 确保原有的readGifFrames方法仍然存在并可用
        try {
            File gifFile = new File("test.gif");
            if (gifFile.exists()) {
                List<BufferedImage> frames = Image2binUtil.readGifFrames(gifFile);
                assertNotNull("readGifFrames方法应该仍然可用", frames);
            }
        } catch (IOException e) {
            // 文件不存在是正常的
        }
    }
}
