package org.example;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * 帧采样测试类
 * 用于验证新的基于FPS的精确帧采样逻辑
 */
public class FrameSamplingTest {

    public static void main(String[] args) {
        System.out.println("=== 帧采样精度测试 ===");
        
        // 测试不同的采样场景
        testSamplingAccuracy();
        
        if (args.length > 0) {
            String testFile = args[0];
            testWithRealFile(testFile);
        } else {
            System.out.println("提示: 可以提供GIF或MP4文件路径作为参数进行实际文件测试");
        }
    }
    
    /**
     * 测试采样精度
     */
    public static void testSamplingAccuracy() {
        System.out.println("\n--- 采样精度测试 ---");
        
        // 测试场景1: 100帧 -> 30帧
        testScenario(100, 30, "标准视频降采样");
        
        // 测试场景2: 60帧 -> 24帧  
        testScenario(60, 24, "高帧率到电影帧率");
        
        // 测试场景3: 120帧 -> 30帧
        testScenario(120, 30, "高帧率大幅降采样");
        
        // 测试场景4: 25帧 -> 15帧
        testScenario(25, 15, "轻度降采样");
        
        // 测试场景5: 边界情况 - 目标帧数大于原始帧数
        testScenario(20, 30, "目标帧数大于原始帧数");
    }
    
    /**
     * 测试特定场景
     */
    private static void testScenario(int originalFrames, int targetFrames, String description) {
        System.out.printf("\n测试: %s (%d -> %d 帧)\n", description, originalFrames, targetFrames);
        
        // 计算理论采样间隔
        double theoreticalInterval = (double) originalFrames / targetFrames;
        System.out.printf("理论采样间隔: %.3f\n", theoreticalInterval);
        
        // 模拟采样过程
        int actualTargetFrames = Math.min(targetFrames, originalFrames);
        System.out.printf("实际目标帧数: %d\n", actualTargetFrames);
        
        // 显示采样的帧索引
        System.out.print("采样帧索引: ");
        for (int i = 0; i < actualTargetFrames; i++) {
            int frameIndex = (int) Math.round(i * theoreticalInterval);
            frameIndex = Math.min(frameIndex, originalFrames - 1);
            System.out.printf("%d ", frameIndex);
            if (i > 0 && i % 10 == 0) System.out.print("\n              ");
        }
        System.out.println();
        
        // 计算实际分布均匀性
        double[] intervals = new double[actualTargetFrames - 1];
        for (int i = 0; i < actualTargetFrames - 1; i++) {
            int currentIndex = (int) Math.round(i * theoreticalInterval);
            int nextIndex = (int) Math.round((i + 1) * theoreticalInterval);
            currentIndex = Math.min(currentIndex, originalFrames - 1);
            nextIndex = Math.min(nextIndex, originalFrames - 1);
            intervals[i] = nextIndex - currentIndex;
        }
        
        if (intervals.length > 0) {
            double avgInterval = 0;
            for (double interval : intervals) {
                avgInterval += interval;
            }
            avgInterval /= intervals.length;
            
            double variance = 0;
            for (double interval : intervals) {
                variance += Math.pow(interval - avgInterval, 2);
            }
            variance /= intervals.length;
            
            System.out.printf("平均间隔: %.3f, 方差: %.3f (越小越均匀)\n", avgInterval, variance);
        }
    }
    
    /**
     * 使用真实文件测试
     */
    public static void testWithRealFile(String filePath) {
        System.out.println("\n--- 真实文件测试 ---");
        System.out.println("测试文件: " + filePath);
        
        File testFile = new File(filePath);
        if (!testFile.exists()) {
            System.out.println("文件不存在: " + filePath);
            return;
        }
        
        try {
            // 读取原始帧
            List<BufferedImage> frames = Image2binUtil.readFrames(testFile);
            System.out.println("原始帧数: " + frames.size());
            
            if (frames.isEmpty()) {
                System.out.println("无法读取帧数据");
                return;
            }
            
            // 测试不同的目标帧数
            int[] targetFrameCounts = {15, 24, 30, 60};
            
            for (int targetFrames : targetFrameCounts) {
                if (targetFrames >= frames.size()) {
                    System.out.printf("跳过测试 %d 帧 (大于原始帧数)\n", targetFrames);
                    continue;
                }
                
                System.out.printf("\n测试降采样到 %d 帧:\n", targetFrames);
                
                // 使用新的采样方法
                long startTime = System.currentTimeMillis();
                byte[] binData = Image2binUtil.convertGif2BinData(
                    testFile, null, null, targetFrames, 
                    Image2binUtil.ColorMode.RGB565A8, true);
                long endTime = System.currentTimeMillis();
                
                if (binData != null) {
                    System.out.printf("转换成功，输出大小: %.2f KB, 耗时: %d ms\n", 
                                    binData.length / 1024.0, endTime - startTime);
                } else {
                    System.out.println("转换失败");
                }
            }
            
        } catch (Exception e) {
            System.out.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
